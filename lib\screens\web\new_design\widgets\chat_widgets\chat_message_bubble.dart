import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/build_question_formatted_text_spans.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/message_content_with_hover.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/quick_message_button.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/selectable_type_writer_text_widget.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/logger.dart';
import 'package:provider/provider.dart';

// ignore: must_be_immutable
class ChatMessageBubbleNew extends StatefulWidget {
  ChatMessageBubbleNew({
    super.key,
    required this.message,
    // required this.nslThinkingExpanded,
    required this.index,
    required this.isLastItem,
    required this.multimediaService,
    this.isAudioLoading = false,
    required this.parentState,
    this.onComplete,
    this.onNewLine,
    this.brdTap,
    this.suggestionsAsCheckbox = false,
  });

  ChatMessage message;
  // Map nslThinkingExpanded;
  int index;
  bool isLastItem;
  MultimediaService multimediaService;
  bool isAudioLoading;
  dynamic parentState;
  final VoidCallback? onComplete;
  final VoidCallback? onNewLine;
  final VoidCallback? brdTap;
  final bool suggestionsAsCheckbox;

  @override
  State<ChatMessageBubbleNew> createState() => _ChatMessageBubbleNewState();
}

class _ChatMessageBubbleNewState extends State<ChatMessageBubbleNew> {
  @override
  Widget build(BuildContext context) {
    // Debug the message content
    Logger.info(
        'Building message bubble with content: "${widget.message.content.length > 50 ? '${widget.message.content.substring(0, 50)}...' : widget.message.content}"');

    // Initialize expansion state for this message if not already set
    // if (!widget.nslThinkingExpanded.containsKey(widget.index)) {
    //   widget.nslThinkingExpanded[widget.index] = false;
    // }
    final contentWidget = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: MessageContentWithHover(
            message: widget.message,
            index: widget.index,
            audioPlayer: widget.multimediaService.audioPlayer,
            currentPlayingMessageId:
                widget.multimediaService.currentPlayingMessageId,
            isPlaying: widget.multimediaService.isPlaying,
            isPaused: widget.multimediaService.isPaused,
            currentPosition: widget.multimediaService.currentPosition,
            totalDuration: widget.multimediaService.totalDuration,
            onTextToSpeech: _convertTextToSpeech,
            onStopAudio: () => widget.multimediaService.stopAudio(),
            showCopyOverlay: _showCopyOverlay,
            isLastItem: widget.isLastItem,
            parentState: widget.parentState,
            onComplete: () {
              widget.onComplete?.call();
              // Trigger rebuild to show follow-up suggestions
              setState(() {});
            },
            onNewLine: () => widget.onNewLine?.call(),
            brdTap: widget.brdTap,
            suggestionsAsCheckbox: widget.suggestionsAsCheckbox,
          ),
        ),
      ],
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.message.isUser)
          // Use IntrinsicWidth to make the container take only the width it needs
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              // Remove SizedBox wrapper and use constraints to limit max width
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width *
                    0.7, // Limit to 70% of screen width
              ),
              decoration: BoxDecoration(
                color: Color(0xFFE9F2F7),
                borderRadius: BorderRadius.circular(AppSpacing.xxs),
              ),
              // margin: EdgeInsets.only(left: 24),
              padding: EdgeInsets.only(
                left: AppSpacing.xs,
                right: 20,
                top: AppSpacing.xs,
                bottom: AppSpacing.xs,
              ),

              child: Row(
                mainAxisSize:
                    MainAxisSize.min, // This makes the Row take minimum width
                crossAxisAlignment:
                    CrossAxisAlignment.start, // Center align items vertically
                mainAxisAlignment:
                    MainAxisAlignment.start, // Start align items horizontally
                children: [
                  // Adjust the avatar to match the roles table alignment
                  SizedBox(
                    width: 30, // Match the checkbox width in roles table
                    child: CircleAvatar(
                      backgroundColor: Color(0xFF0058FF),
                      radius: 12,
                      child: Text(
                        Provider.of<AuthProvider>(context)
                                .user
                                ?.firstName?[0]
                                .capitalize() ??
                            '',
                        style: TextStyle(
                          // fontFamily: 'TiemposText',
                          color: Colors.white,
                          fontSize: MediaQuery.of(context).size.width > 1600
                              ? 17
                              : 15,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: AppSpacing.xxs),
                  // Remove Expanded to allow natural width
                  Flexible(
                    child: SelectableText(
                      widget.message.content,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        // fontFamily: ''
                        // letterSpacing: 1.2,
                        fontSize:
                            MediaQuery.of(context).size.width > 1600 ? 17 : 15,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        else

          // NSL response
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // NSL Thinking expandable
              widget.message.reasoningData != null &&
                      widget.message.reasoningData!.isNotEmpty
                  ? Container(
                      alignment: Alignment.centerRight,
                      child: AnimatedContainer(
                        duration: Duration(milliseconds: 600),
                        curve: Curves.easeInOut,
                        width: widget.message.isReasoningDataExpanded == true
                            ? double.infinity
                            : null,
                        margin: const EdgeInsets.only(top: AppSpacing.xs),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: CustomExpansionTile(
                          backgroundColor: Colors.grey.shade100,
                          initiallyExpanded:
                              widget.message.isReasoningDataExpanded ?? false,
                          onExpansionChanged: (expanded) {
                            setState(() {
                              widget.message.isReasoningDataExpanded = expanded;
                              if (expanded &&
                                  !(widget.message.hasAnimatedThinking ??
                                      false)) {
                                widget.message.hasAnimatedThinking = true;
                              }
                            });
                          },
                          onTitleTap: () {},
                          title: Container(
                            color: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                vertical: 12.0, horizontal: 12.0),
                            child: Text(
                              "NSL Thinking",
                              style: TextStyle(
                                fontFamily: 'TiemposText',
                                fontSize:
                                    MediaQuery.of(context).size.width > 1600
                                        ? 14
                                        : 12,
                                fontWeight: FontWeight.w400,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          children: [
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: widget.message.reasoningData!
                                    .map((reasoning) {
                                  final baseStyle = TextStyle(
                                    fontFamily: 'TiemposText',
                                    fontSize:
                                        MediaQuery.of(context).size.width > 1600
                                            ? 16
                                            : 14,
                                  );

                                  final spans = buildQuestionFormattedTextSpans(
                                      reasoning.replaceFirstMapped(
                                        "\n",
                                        (match) => "",
                                      ),
                                      baseStyle);

                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        widget.message.reasoningData!.length ==
                                                1
                                            ? SizedBox()
                                            : Text('• ',
                                                style: baseStyle.copyWith(
                                                    fontWeight:
                                                        FontWeight.bold)),
                                        Expanded(
                                          child: widget.message
                                                      .hasAnimatedThinking ??
                                                  false
                                              ? SelectableText.rich(
                                                  TextSpan(children: spans),
                                                )
                                              : SelectableTypewriterText(
                                                  textSpans: spans,
                                                  speed: Duration(
                                                    milliseconds: reasoning
                                                                .length >
                                                            500
                                                        ? (reasoning.length /
                                                                200)
                                                            .toInt()
                                                        : (reasoning.length /
                                                                50)
                                                            .toInt(),
                                                  ),
                                                  onComplete: reasoning ==
                                                          widget
                                                              .message
                                                              .reasoningData!
                                                              .last
                                                      ? _onNSLTypingComplete
                                                      : null,
                                                ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : SizedBox(),

              // Spacing
              SizedBox(height: AppSpacing.xxs / 2),

              // Message content — only shown after NSL typing finishes
              widget.message.reasoningData != null &&
                      widget.message.reasoningData!.isNotEmpty
                  ? widget.message.hasNSLTypingCompleted ?? false
                      ? contentWidget
                      : Container()
                  : contentWidget,

              SizedBox(height: AppSpacing.xxs),

              SizedBox(height: AppSpacing.xxs),
            ],
          ),
        SizedBox(
          height: AppSpacing.xs,
        )
      ],
    );
  }

  void _onNSLTypingComplete() {
    setState(() {
      widget.message.hasAnimatedThinking = false;
      widget.message.hasNSLTypingCompleted = true;
      widget.message.isReasoningDataExpanded = false; // auto-collapse
    });
  }

  // Convert text to speech and play audio
  Future<void> _convertTextToSpeech(String text, {String? messageId}) async {
    if (text.isEmpty) return;

    // Show audio loading indicator
    setState(() {
      widget.isAudioLoading = true;
    });

    try {
      // Call the multimedia service to convert text to speech
      await widget.multimediaService.convertTextToSpeech(
        text,
        messageId: messageId,
        context: context,
      );
    } finally {
      // Hide audio loading indicator
      setState(() {
        widget.isAudioLoading = false;
      });
    }
  }

  // Show a small overlay notification for copy action
  void _showCopyOverlay(BuildContext context) {
    // Create an overlay entry
    final OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 50,
        right: 50,
        child: Material(
          elevation: 4.0,
          borderRadius: BorderRadius.circular(8),
          color: Colors.black87,
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  'Copied to clipboard',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 2 seconds
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}
